{"devDependencies": {"@balancer-labs/sdk": "^1.1.6", "@balancer-labs/v2-interfaces": "^0.4.0", "@balancer-labs/v2-vault": "^3.0.1", "@nomicfoundation/hardhat-ignition": "^3.0.0", "@nomicfoundation/hardhat-toolbox-viem": "^5.0.0", "@types/node": "^22.18.0", "dotenv": "^17.2.1", "forge-std": "github:foundry-rs/forge-std#v1.9.4", "hardhat": "^3.0.1", "typescript": "~5.8.0", "viem": "^2.35.1"}, "type": "module", "dependencies": {"@aave/core-v3": "^1.19.3", "@openzeppelin/contracts": "^5.4.0", "@uniswap/v3-periphery": "^1.4.4"}}